<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="log"></div>
    
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            console.log(message);
            logDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        // Test with a dummy token (this will fail auth but should connect)
        const socket = io('http://localhost:3001', {
            auth: {
                token: 'test-token'
            },
            transports: ['websocket', 'polling'],
            timeout: 20000,
            reconnection: true,
            reconnectionAttempts: 3,
            reconnectionDelay: 1000,
        });
        
        socket.on('connect', () => {
            statusDiv.textContent = 'Connected!';
            statusDiv.style.color = 'green';
            log('✅ Socket connected: ' + socket.id);
            log('Transport: ' + socket.io.engine.transport.name);
        });
        
        socket.on('disconnect', (reason) => {
            statusDiv.textContent = 'Disconnected: ' + reason;
            statusDiv.style.color = 'red';
            log('❌ Socket disconnected: ' + reason);
        });
        
        socket.on('connect_error', (error) => {
            statusDiv.textContent = 'Connection Error: ' + error.message;
            statusDiv.style.color = 'red';
            log('🔌 Connection error: ' + error.message);
            log('Error details: ' + JSON.stringify(error, null, 2));
        });
        
        socket.on('error', (error) => {
            log('🚨 Socket error: ' + error.message || error);
        });
        
        // Test WebSocket directly
        log('Testing direct WebSocket connection...');
        try {
            const ws = new WebSocket('ws://localhost:3001/socket.io/?EIO=4&transport=websocket');
            
            ws.onopen = function() {
                log('✅ Direct WebSocket connection opened');
                ws.close();
            };
            
            ws.onerror = function(error) {
                log('❌ Direct WebSocket error: ' + error);
            };
            
            ws.onclose = function(event) {
                log('🔌 Direct WebSocket closed: ' + event.code + ' - ' + event.reason);
            };
        } catch (error) {
            log('❌ Direct WebSocket failed: ' + error.message);
        }
    </script>
</body>
</html>
