(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/client-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>ClientProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function ClientProvider(param) {
    let { children } = param;
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "ClientProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]()
    }["ClientProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/client-provider.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_s(ClientProvider, "qFhNRSk+4hqflxYLL9+zYF5AcuQ=");
_c = ClientProvider;
var _c;
__turbopack_context__.k.register(_c, "ClientProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": ()=>api,
    "apiCall": ()=>apiCall,
    "authAPI": ()=>authAPI,
    "axiosInstance": ()=>axiosInstance,
    "reviewAPI": ()=>reviewAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
const API_URL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || "http://localhost:3001";
// Create axios instance with default configuration
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_URL,
    headers: {
        "Content-Type": "application/json"
    },
    timeout: 30000
});
// Helper function to get authentication token
function getAuthToken() {
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem("token");
    }
    //TURBOPACK unreachable
    ;
}
// Request interceptor to add auth token
axiosInstance.interceptors.request.use((config)=>{
    var _config_method, _config_metadata;
    const authToken = getAuthToken();
    // Debug logging
    console.log("🔍 API Call Debug:", {
        endpoint: config.url,
        fullUrl: "".concat(API_URL).concat(config.url),
        API_URL,
        hasToken: !!authToken,
        tokenPreview: authToken ? authToken.substring(0, 3) + "***" : "none",
        method: (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(),
        data: config.data,
        "process.env.NEXT_PUBLIC_API_URL": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL
    });
    // Add auth token if available and not skipped
    if (authToken && !((_config_metadata = config.metadata) === null || _config_metadata === void 0 ? void 0 : _config_metadata.skipAuth)) {
        config.headers.Authorization = "Bearer ".concat(authToken);
    }
    return config;
}, (error)=>{
    console.error("💥 Request interceptor error:", error);
    return Promise.reject(error);
});
// Response interceptor for logging and error handling
axiosInstance.interceptors.response.use((response)=>{
    console.log("📡 Response status:", response.status, response.statusText);
    console.log("✅ API Success:", response.data);
    return response;
}, (error)=>{
    var _error_response, _error_response_data, _error_response1, _error_response2;
    console.error("❌ API Error:", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);
    console.error("💥 API call failed:", error);
    // Extract error message
    const errorMessage = ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || "HTTP error! status: ".concat((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status);
    throw new Error(errorMessage);
});
async function apiCall(endpoint) {
    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    const { method = "GET", body, headers = {}, skipAuth = false } = options;
    const config = {
        method: method.toLowerCase(),
        url: endpoint,
        headers: {
            ...headers
        },
        metadata: {
            skipAuth
        }
    };
    // Add data for non-GET requests
    if (body && method !== "GET") {
        config.data = body;
    }
    try {
        const response = await axiosInstance(config);
        return response.data;
    } catch (error) {
        throw error;
    }
}
const api = {
    // Generic HTTP methods
    get: function(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return apiCall(endpoint, {
            ...options,
            method: "GET"
        });
    },
    post: function(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return apiCall(endpoint, {
            ...options,
            method: "POST"
        });
    },
    put: function(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return apiCall(endpoint, {
            ...options,
            method: "PUT"
        });
    },
    delete: function(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return apiCall(endpoint, {
            ...options,
            method: "DELETE"
        });
    },
    // Health check (no token required)
    health: ()=>apiCall("/health"),
    // Chat endpoints (token required)
    chat: (message, threadId, expertId, expertContext)=>apiCall("/api/chat", {
            method: "POST",
            body: {
                message,
                threadId,
                expertId,
                expertContext
            }
        }),
    getThreadMessages: (threadId)=>apiCall("/api/thread/".concat(threadId, "/messages")),
    getSessionMessages: (sessionId, limit)=>apiCall("/api/chat/sessions/".concat(sessionId, "/messages").concat(limit ? "?limit=".concat(limit) : "")),
    // Chat session endpoints
    getUserChatSessions: (limit)=>apiCall("/api/chat/sessions".concat(limit ? "?limit=".concat(limit) : "")),
    getUserStats: ()=>apiCall("/api/chat/stats"),
    getActiveSessionForExpert: (expertId)=>apiCall("/api/chat/sessions/expert/".concat(expertId)),
    updateSessionTitle: (sessionId, title)=>apiCall("/api/chat/sessions/".concat(sessionId, "/title"), {
            method: "PUT",
            body: {
                title
            }
        }),
    deleteSession: (sessionId)=>apiCall("/api/chat/sessions/".concat(sessionId), {
            method: "DELETE"
        }),
    // Assistant endpoints (token required)
    createThread: ()=>apiCall("/assistant/thread", {
            method: "POST"
        }),
    sendMessage: (threadId, message)=>apiCall("/assistant/message", {
            method: "POST",
            body: {
                threadId,
                message
            }
        }),
    runAssistant: (threadId)=>apiCall("/assistant/run", {
            method: "POST",
            body: {
                threadId
            }
        }),
    getMessages: (threadId)=>apiCall("/assistant/messages/".concat(threadId)),
    // Expert endpoints (token required)
    createExpert: async (expertData)=>{
        const authToken = getAuthToken();
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(API_URL, "/api/experts"), expertData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                    ...authToken ? {
                        Authorization: "Bearer ".concat(authToken)
                    } : {}
                }
            });
            return response.data;
        } catch (error) {
            var _error_response_data, _error_response, _error_response1;
            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || "HTTP error! status: ".concat((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);
            throw new Error(errorMessage);
        }
    },
    listExperts: ()=>apiCall("/api/experts"),
    // Get public experts (no authentication required)
    getPublicExperts: ()=>apiCall("/api/experts/public", {
            skipAuth: true
        }),
    getExpert: (expertId)=>apiCall("/api/experts/".concat(expertId)),
    updateExpert: async (expertId, expertData, knowledgeBaseFile, imageFile)=>{
        const authToken = getAuthToken();
        const formData = new FormData();
        // Add text fields
        Object.keys(expertData).forEach((key)=>{
            if (expertData[key] !== undefined && expertData[key] !== null) {
                if (key === "labels" && Array.isArray(expertData[key])) {
                    formData.append(key, JSON.stringify(expertData[key]));
                } else {
                    formData.append(key, expertData[key].toString());
                }
            }
        });
        // Add files
        if (knowledgeBaseFile) {
            formData.append("file", knowledgeBaseFile);
        }
        if (imageFile) {
            formData.append("image", imageFile);
        }
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put("".concat(API_URL, "/api/experts/").concat(expertId), formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                    ...authToken ? {
                        Authorization: "Bearer ".concat(authToken)
                    } : {}
                }
            });
            return response.data;
        } catch (error) {
            var _error_response_data, _error_response, _error_response1;
            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || "HTTP error! status: ".concat((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);
            throw new Error(errorMessage);
        }
    },
    // Model endpoints
    getAvailableModels: ()=>apiCall("/api/models"),
    getModelPricing: (model)=>apiCall("/api/models/".concat(model, "/pricing")),
    calculateCost: (model, inputTokens, outputTokens, pricingPercentage)=>apiCall("/api/calculate-cost", {
            method: "POST",
            body: {
                model,
                inputTokens,
                outputTokens,
                pricingPercentage
            }
        }),
    // Expert statistics endpoints
    getExpertStats: (expertId)=>apiCall("/api/experts/".concat(expertId, "/stats")),
    // Review endpoints (added directly to avoid type issues)
    createReview: (reviewData)=>apiCall("/api/reviews", {
            method: "POST",
            body: reviewData
        }),
    updateReview: (reviewId, reviewData)=>apiCall("/api/reviews/".concat(reviewId), {
            method: "PUT",
            body: reviewData
        }),
    getExpertReviews: function(expertId) {
        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;
        return apiCall("/api/reviews/expert/".concat(expertId, "?page=").concat(page, "&limit=").concat(limit), {
            skipAuth: true
        });
    },
    getUserReviews: function() {
        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;
        return apiCall("/api/reviews/my?page=".concat(page, "&limit=").concat(limit));
    },
    getReview: (reviewId)=>apiCall("/api/reviews/".concat(reviewId), {
            skipAuth: true
        }),
    canUserReview: (expertId)=>apiCall("/api/reviews/expert/".concat(expertId, "/can-review")),
    getExpertRatingStats: (expertId)=>apiCall("/api/reviews/expert/".concat(expertId, "/stats"), {
            skipAuth: true
        })
};
const authAPI = {
    // Register new user
    register: (userData)=>apiCall("/api/users/register", {
            method: "POST",
            body: userData,
            skipAuth: true
        }),
    // Verify OTP
    verifyOTP: (data)=>apiCall("/api/users/verify-otp", {
            method: "POST",
            body: data,
            skipAuth: true
        }),
    // Login user
    login: (credentials)=>apiCall("/api/users/login", {
            method: "POST",
            body: credentials,
            skipAuth: true
        }),
    // Get current user profile
    getProfile: ()=>apiCall("/api/users/profile"),
    // Update user profile
    updateProfile: (profileData)=>apiCall("/api/users/profile", {
            method: "PUT",
            body: profileData
        }),
    // Change password
    changePassword: (passwordData)=>apiCall("/api/users/change-password", {
            method: "POST",
            body: passwordData
        }),
    // Resend OTP
    resendOTP: (phone)=>apiCall("/api/users/resend-otp", {
            method: "POST",
            body: {
                phone
            },
            skipAuth: true
        }),
    // Forgot password
    forgotPassword: (phone)=>apiCall("/api/users/forgot-password", {
            method: "POST",
            body: {
                phone
            },
            skipAuth: true
        }),
    // Reset password
    resetPassword: (phone, code, newPassword)=>apiCall("/api/users/reset-password", {
            method: "POST",
            body: {
                phone,
                code,
                newPassword
            },
            skipAuth: true
        }),
    // Logout
    logout: ()=>apiCall("/api/users/logout", {
            method: "POST"
        }),
    // Balance endpoints
    getBalanceSummary: ()=>apiCall("/api/balance/summary"),
    getPointTransactions: (limit)=>apiCall("/api/balance/transactions/points".concat(limit ? "?limit=".concat(limit) : "")),
    getCreditTransactions: (limit)=>apiCall("/api/balance/transactions/credits".concat(limit ? "?limit=".concat(limit) : "")),
    checkAffordability: (amount)=>apiCall("/api/balance/can-afford", {
            method: "POST",
            body: {
                amount
            }
        }),
    addPoints: (amount, description)=>apiCall("/api/balance/points/add", {
            method: "POST",
            body: {
                amount,
                description
            }
        }),
    addCredits: (amount, description)=>apiCall("/api/balance/credits/add", {
            method: "POST",
            body: {
                amount,
                description
            }
        })
};
const reviewAPI = {
    // Create a new review
    createReview: (reviewData)=>apiCall("/api/reviews", {
            method: "POST",
            body: reviewData
        }),
    // Update an existing review
    updateReview: (reviewId, reviewData)=>apiCall("/api/reviews/".concat(reviewId), {
            method: "PUT",
            body: reviewData
        }),
    // Get reviews for an expert
    getExpertReviews: function(expertId) {
        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;
        return apiCall("/api/reviews/expert/".concat(expertId, "?page=").concat(page, "&limit=").concat(limit), {
            skipAuth: true
        });
    },
    // Get current user's reviews
    getUserReviews: function() {
        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;
        return apiCall("/api/reviews/my?page=".concat(page, "&limit=").concat(limit));
    },
    // Get review by ID
    getReview: (reviewId)=>apiCall("/api/reviews/".concat(reviewId), {
            skipAuth: true
        }),
    // Check if user can review an expert
    canUserReview: (expertId)=>apiCall("/api/reviews/expert/".concat(expertId, "/can-review")),
    // Get expert rating statistics
    getExpertRatingStats: (expertId)=>apiCall("/api/reviews/expert/".concat(expertId, "/stats"), {
            skipAuth: true
        }),
    // Admin functions
    hideReview: (reviewId)=>apiCall("/api/reviews/admin/".concat(reviewId, "/hide"), {
            method: "PUT"
        }),
    showReview: (reviewId)=>apiCall("/api/reviews/admin/".concat(reviewId, "/show"), {
            method: "PUT"
        }),
    deleteReview: (reviewId)=>apiCall("/api/reviews/admin/".concat(reviewId), {
            method: "DELETE"
        }),
    getPendingReviews: function() {
        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;
        return apiCall("/api/reviews/admin/pending?page=".concat(page, "&limit=").concat(limit));
    }
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = (param)=>{
    let { children } = param;
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [token, setToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize auth state from localStorage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initAuth = {
                "AuthProvider.useEffect.initAuth": async ()=>{
                    const storedToken = localStorage.getItem('token');
                    const storedUser = localStorage.getItem('user');
                    if (storedToken && storedUser) {
                        try {
                            setToken(storedToken);
                            setUser(JSON.parse(storedUser));
                            // Verify token is still valid by fetching profile
                            const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].getProfile();
                            setUser(profile.user);
                        } catch (error) {
                            console.error('Token validation failed:', error);
                            // Clear invalid token
                            localStorage.removeItem('token');
                            localStorage.removeItem('user');
                            setToken(null);
                            setUser(null);
                        }
                    }
                    setIsLoading(false);
                }
            }["AuthProvider.useEffect.initAuth"];
            initAuth();
        }
    }["AuthProvider.useEffect"], []);
    const login = async (phone, password)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].login({
                phone,
                password
            });
            const userData = response.user; // Backend returns user data directly
            const userToken = userData.token; // Token is within the user object
            // Remove token from user data for storage
            const userDataForStorage = {
                user_id: userData.user_id,
                phone: userData.phone,
                name: userData.name,
                email: userData.email
            };
            setUser(userDataForStorage);
            setToken(userToken);
            // Store in localStorage
            localStorage.setItem('token', userToken);
            localStorage.setItem('user', JSON.stringify(userDataForStorage));
        } catch (error) {
            throw new Error(error.message || 'Login failed');
        }
    };
    const register = async (userData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].register(userData);
            return response;
        } catch (error) {
            throw new Error(error.message || 'Registration failed');
        }
    };
    const verifyOTP = async (phone, code)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].verifyOTP({
                phone,
                code
            });
            const userData = response.user; // Backend returns user data directly
            const userToken = userData.token; // Token is within the user object
            // Remove token from user data for storage
            const userDataForStorage = {
                user_id: userData.user_id,
                phone: userData.phone,
                name: userData.name,
                email: userData.email
            };
            setUser(userDataForStorage);
            setToken(userToken);
            // Store in localStorage
            localStorage.setItem('token', userToken);
            localStorage.setItem('user', JSON.stringify(userDataForStorage));
        } catch (error) {
            throw new Error(error.message || 'OTP verification failed');
        }
    };
    const logout = async ()=>{
        try {
            if (token) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].logout();
            }
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally{
            // Clear state regardless of API call success
            setUser(null);
            setToken(null);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        }
    };
    const resendOTP = async (phone)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].resendOTP(phone);
            return response;
        } catch (error) {
            throw new Error(error.message || 'Failed to resend OTP');
        }
    };
    const forgotPassword = async (phone)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].forgotPassword(phone);
            return response;
        } catch (error) {
            throw new Error(error.message || 'Failed to request password reset');
        }
    };
    const resetPassword = async (phone, code, newPassword)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].resetPassword(phone, code, newPassword);
        } catch (error) {
            throw new Error(error.message || 'Password reset failed');
        }
    };
    const value = {
        user,
        token,
        isLoading,
        isAuthenticated: !!user && !!token,
        login,
        register,
        verifyOTP,
        logout,
        resendOTP,
        forgotPassword,
        resetPassword
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 176,
        columnNumber: 9
    }, ("TURBOPACK compile-time value", void 0));
};
_s(AuthProvider, "mX4/AXRUN66G8j/NKXHYWKblzjI=");
_c = AuthProvider;
const useAuth = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Navigation.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wallet.js [app-client] (ecmascript) <export default as Wallet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$in$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogIn$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-in.js [app-client] (ecmascript) <export default as LogIn>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserPlus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-plus.js [app-client] (ecmascript) <export default as UserPlus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
const Navigation = ()=>{
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, isAuthenticated, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [showExpertsDropdown, setShowExpertsDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showUserDropdown, setShowUserDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const navItems = [
        {
            href: "/ai-experts",
            label: "AI Experts"
        },
        // History menu only for authenticated users
        ...isAuthenticated ? [
            {
                href: "/history",
                label: "History"
            }
        ] : []
    ];
    const expertItems = [
        {
            href: "/experts?view=overview",
            label: "Overview"
        },
        {
            href: "/experts?view=manage",
            label: "Manage Expert"
        }
    ];
    const userMenuItems = [
        {
            href: "/profile",
            label: "My Profile",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"]
        },
        {
            href: "/affiliate",
            label: "Affiliate Program",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"]
        },
        {
            href: "/balance",
            label: "Saldo",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__["Wallet"]
        }
    ];
    const handleLogout = async ()=>{
        await logout();
        setShowUserDropdown(false);
        router.push("/");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        className: "bg-white shadow-lg border-b border-gray-100",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center h-16",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "text-2xl font-bold",
                                style: {
                                    color: "#1E3A8A"
                                },
                                children: "PakarAI"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 52,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex space-x-6",
                                children: [
                                    navItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            className: "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(pathname === item.href ? "text-white shadow-lg" : "text-gray-600 hover:text-white hover:shadow-md"),
                                            style: pathname === item.href ? {
                                                backgroundColor: "#1E3A8A"
                                            } : {
                                                backgroundColor: "transparent"
                                            },
                                            onMouseEnter: (e)=>{
                                                if (pathname !== item.href) {
                                                    e.currentTarget.style.backgroundColor = "#1E3A8A";
                                                }
                                            },
                                            onMouseLeave: (e)=>{
                                                if (pathname !== item.href) {
                                                    e.currentTarget.style.backgroundColor = "transparent";
                                                }
                                            },
                                            children: item.label
                                        }, item.href, false, {
                                            fileName: "[project]/src/components/Navigation.tsx",
                                            lineNumber: 62,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))),
                                    isAuthenticated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        onMouseEnter: ()=>setShowExpertsDropdown(true),
                                        onMouseLeave: ()=>setShowExpertsDropdown(false),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1 ".concat(pathname.startsWith("/experts") ? "text-white shadow-lg" : "text-gray-600 hover:text-white hover:shadow-md"),
                                                style: pathname.startsWith("/experts") ? {
                                                    backgroundColor: "#1E3A8A"
                                                } : {
                                                    backgroundColor: "transparent"
                                                },
                                                onMouseEnter: (e)=>{
                                                    if (!pathname.startsWith("/experts")) {
                                                        e.currentTarget.style.backgroundColor = "#1E3A8A";
                                                    }
                                                },
                                                onMouseLeave: (e)=>{
                                                    if (!pathname.startsWith("/experts")) {
                                                        e.currentTarget.style.backgroundColor = "transparent";
                                                    }
                                                },
                                                children: [
                                                    "My Experts",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                        className: "w-4 h-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/Navigation.tsx",
                                                        lineNumber: 120,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 97,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            showExpertsDropdown && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute top-full left-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",
                                                children: expertItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        className: "block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",
                                                        children: item.label
                                                    }, item.href, false, {
                                                        fileName: "[project]/src/components/Navigation.tsx",
                                                        lineNumber: 126,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0)))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 124,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 92,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 60,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Navigation.tsx",
                        lineNumber: 51,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    isAuthenticated && user ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative",
                        onMouseEnter: ()=>setShowUserDropdown(true),
                        onMouseLeave: ()=>setShowUserDropdown(false),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                            className: "w-4 h-4 text-white"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Navigation.tsx",
                                            lineNumber: 150,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 149,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "hidden md:block",
                                        children: user.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 152,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 153,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            showUserDropdown && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute top-full right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-4 py-2 border-b border-gray-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-900",
                                                children: user.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 159,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500",
                                                children: user.email
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 162,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 158,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard",
                                        className: "flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 169,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Dashboard"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 170,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 165,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    userMenuItems.map((item)=>{
                                        const IconComponent = item.icon;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            className: "flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/Navigation.tsx",
                                                    lineNumber: 181,
                                                    columnNumber: 25
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: item.label
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/Navigation.tsx",
                                                    lineNumber: 182,
                                                    columnNumber: 25
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, item.href, true, {
                                            fileName: "[project]/src/components/Navigation.tsx",
                                            lineNumber: 176,
                                            columnNumber: 23
                                        }, ("TURBOPACK compile-time value", void 0));
                                    }),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                        className: "my-1 border-gray-200"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 186,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleLogout,
                                        className: "flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 191,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Logout"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 192,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 187,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 157,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Navigation.tsx",
                        lineNumber: 143,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/login",
                                className: "flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$in$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogIn$3e$__["LogIn"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 203,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Login"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 204,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 199,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/register",
                                className: "flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserPlus$3e$__["UserPlus"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 210,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Sign Up"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 211,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 206,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Navigation.tsx",
                        lineNumber: 198,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Navigation.tsx",
                lineNumber: 50,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/Navigation.tsx",
            lineNumber: 49,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/Navigation.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(Navigation, "lO5BbuonPKyUrDrr+kAVXFGvCjc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = Navigation;
const __TURBOPACK__default__export__ = Navigation;
var _c;
__turbopack_context__.k.register(_c, "Navigation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SocketContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SocketProvider": ()=>SocketProvider,
    "default": ()=>__TURBOPACK__default__export__,
    "useSocket": ()=>useSocket
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
const SocketContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    socket: null,
    isConnected: false,
    connectionError: null,
    joinChat: ()=>{},
    leaveChat: ()=>{},
    sendChatMessage: ()=>{},
    currentChatRoom: null,
    reconnect: ()=>{}
});
const useSocket = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SocketContext);
    if (!context) {
        throw new Error('useSocket must be used within a SocketProvider');
    }
    return context;
};
_s(useSocket, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const SocketProvider = (param)=>{
    let { children } = param;
    _s1();
    const { token, isAuthenticated, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [socket, setSocket] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isConnected, setIsConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [connectionError, setConnectionError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentChatRoom, setCurrentChatRoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const reconnectAttempts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const maxReconnectAttempts = 5;
    // Initialize socket when auth state changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SocketProvider.useEffect": ()=>{
            console.log('🔌 Socket initialization check:', {
                isLoading,
                isAuthenticated,
                hasToken: !!token,
                tokenLength: token === null || token === void 0 ? void 0 : token.length,
                tokenStart: (token === null || token === void 0 ? void 0 : token.substring(0, 20)) + '...'
            });
            // Don't initialize if auth is still loading
            if (isLoading) {
                console.log('🔌 Auth still loading, waiting...');
                return;
            }
            // Clean up existing socket first
            if (socket) {
                console.log('🧹 Cleaning up existing socket connection');
                socket.disconnect();
                setSocket(null);
                setIsConnected(false);
                setCurrentChatRoom(null);
            }
            if (!isAuthenticated || !token) {
                console.log('🔌 User not authenticated, skipping socket connection');
                setConnectionError('Please log in to use real-time chat');
                return;
            }
            const API_URL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
            console.log('🔌 Initializing Socket.IO connection to:', API_URL);
            // Create socket connection
            const newSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(API_URL, {
                auth: {
                    token: token
                },
                transports: [
                    'polling',
                    'websocket'
                ],
                timeout: 20000,
                reconnection: true,
                reconnectionAttempts: maxReconnectAttempts,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                upgrade: true,
                rememberUpgrade: false
            });
            // Connection event handlers
            newSocket.on('connect', {
                "SocketProvider.useEffect": ()=>{
                    console.log('✅ Socket connected:', newSocket.id);
                    console.log('🚀 Transport used:', newSocket.io.engine.transport.name);
                    console.log('🔗 Socket URL:', newSocket.io.uri);
                    setIsConnected(true);
                    setConnectionError(null);
                    reconnectAttempts.current = 0;
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('disconnect', {
                "SocketProvider.useEffect": (reason)=>{
                    console.log('❌ Socket disconnected:', reason);
                    setIsConnected(false);
                    setCurrentChatRoom(null);
                    if (reason === 'io server disconnect') {
                        // Server initiated disconnect, try to reconnect
                        newSocket.connect();
                    }
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('connect_error', {
                "SocketProvider.useEffect": (error)=>{
                    console.error('🔌 Socket connection error:', error);
                    console.error('🔌 Error details:', {
                        message: error.message,
                        name: error.name,
                        stack: error.stack
                    });
                    setConnectionError(error.message || 'Connection failed');
                    setIsConnected(false);
                    reconnectAttempts.current += 1;
                    if (reconnectAttempts.current >= maxReconnectAttempts) {
                        setConnectionError('Failed to connect after multiple attempts');
                    }
                }
            }["SocketProvider.useEffect"]);
            // Chat event handlers
            newSocket.on('chat_joined', {
                "SocketProvider.useEffect": (data)=>{
                    console.log('🏠 Joined chat room:', data);
                    setCurrentChatRoom(data.room);
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('chat_error', {
                "SocketProvider.useEffect": (data)=>{
                    console.error('💬 Chat error:', data);
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('typing_start', {
                "SocketProvider.useEffect": (data)=>{
                    console.log('⌨️ User started typing:', data);
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('typing_stop', {
                "SocketProvider.useEffect": (data)=>{
                    console.log('⌨️ User stopped typing:', data);
                }
            }["SocketProvider.useEffect"]);
            // Authentication error handler
            newSocket.on('error', {
                "SocketProvider.useEffect": (error)=>{
                    console.error('🔐 Socket authentication error:', error);
                    if (error.message && error.message.includes('token')) {
                        setConnectionError('Authentication failed - please log in again');
                        // Clear invalid token
                        localStorage.removeItem('token');
                    } else {
                        setConnectionError('Connection failed');
                    }
                    setIsConnected(false);
                }
            }["SocketProvider.useEffect"]);
            // Transport upgrade events
            newSocket.io.engine.on('upgrade', {
                "SocketProvider.useEffect": ()=>{
                    console.log('🔄 Transport upgraded to:', newSocket.io.engine.transport.name);
                }
            }["SocketProvider.useEffect"]);
            newSocket.io.engine.on('upgradeError', {
                "SocketProvider.useEffect": (error)=>{
                    console.error('❌ Transport upgrade failed:', error);
                }
            }["SocketProvider.useEffect"]);
            setSocket(newSocket);
            // Cleanup function
            return ({
                "SocketProvider.useEffect": ()=>{
                    console.log('🧹 Cleaning up socket connection on unmount/change');
                    newSocket.disconnect();
                }
            })["SocketProvider.useEffect"];
        }
    }["SocketProvider.useEffect"], [
        isLoading,
        isAuthenticated,
        token,
        socket
    ]); // Direct dependencies instead of callback
    const joinChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocketProvider.useCallback[joinChat]": (expertId, sessionId)=>{
            if (!socket || !isConnected) {
                console.warn('⚠️ Cannot join chat: socket not connected');
                return;
            }
            console.log('🏠 Joining chat:', {
                expertId,
                sessionId
            });
            socket.emit('join_chat', {
                expertId,
                sessionId
            });
        }
    }["SocketProvider.useCallback[joinChat]"], [
        socket,
        isConnected
    ]);
    const leaveChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocketProvider.useCallback[leaveChat]": ()=>{
            if (!socket || !isConnected) {
                console.warn('⚠️ Cannot leave chat: socket not connected');
                return;
            }
            console.log('🚪 Leaving chat');
            socket.emit('leave_chat');
            setCurrentChatRoom(null);
        }
    }["SocketProvider.useCallback[leaveChat]"], [
        socket,
        isConnected
    ]);
    const sendChatMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocketProvider.useCallback[sendChatMessage]": (message, expertId, sessionId)=>{
            if (!socket || !isConnected) {
                console.warn('⚠️ Cannot send message: socket not connected');
                return;
            }
            console.log('📤 Sending chat message:', {
                message: message.substring(0, 50) + '...',
                expertId,
                sessionId
            });
            socket.emit('start_chat_stream', {
                message,
                expertId,
                sessionId
            });
        }
    }["SocketProvider.useCallback[sendChatMessage]"], [
        socket,
        isConnected
    ]);
    const reconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocketProvider.useCallback[reconnect]": ()=>{
            console.log('🔄 Manual reconnection requested');
            if (socket) {
                socket.disconnect();
            }
            setSocket(null);
            setIsConnected(false);
            setConnectionError(null);
            setCurrentChatRoom(null);
            reconnectAttempts.current = 0;
            // Force re-initialization by temporarily clearing and restoring token
            // This will trigger the useEffect to run again
            const currentToken = token;
            if (currentToken) {
                // Small delay to ensure cleanup happens first
                setTimeout({
                    "SocketProvider.useCallback[reconnect]": ()=>{
                        // The useEffect will automatically reinitialize when dependencies change
                        console.log('🔄 Reconnection will be handled by useEffect');
                    }
                }["SocketProvider.useCallback[reconnect]"], 100);
            }
        }
    }["SocketProvider.useCallback[reconnect]"], [
        socket,
        token
    ]);
    const value = {
        socket,
        isConnected,
        connectionError,
        joinChat,
        leaveChat,
        sendChatMessage,
        currentChatRoom,
        reconnect
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SocketContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SocketContext.tsx",
        lineNumber: 253,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s1(SocketProvider, "0F8kkuP/FR80nJv0r1U48YFp2Bk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = SocketProvider;
const __TURBOPACK__default__export__ = SocketContext;
var _c;
__turbopack_context__.k.register(_c, "SocketProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_6bd32203._.js.map