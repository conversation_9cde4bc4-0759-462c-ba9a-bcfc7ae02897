"use client";

import React, { createContext, useContext, useEffect, useState, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  connectionError: string | null;
  joinChat: (expertId: string, sessionId?: string) => void;
  leaveChat: () => void;
  sendChatMessage: (message: string, expertId: string, sessionId?: string) => void;
  currentChatRoom: string | null;
  reconnect: () => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  connectionError: null,
  joinChat: () => {},
  leaveChat: () => {},
  sendChatMessage: () => {},
  currentChatRoom: null,
  reconnect: () => {},
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const { token, isAuthenticated, isLoading } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [currentChatRoom, setCurrentChatRoom] = useState<string | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize socket when auth state changes
  useEffect(() => {
    console.log('🔌 Socket initialization check:', {
      isLoading,
      isAuthenticated,
      hasToken: !!token,
      tokenLength: token?.length,
      tokenStart: token?.substring(0, 20) + '...'
    });
    
    // Don't initialize if auth is still loading
    if (isLoading) {
      console.log('🔌 Auth still loading, waiting...');
      return;
    }
    
    // Clean up existing socket first
    if (socket) {
      console.log('🧹 Cleaning up existing socket connection');
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      setCurrentChatRoom(null);
    }
    
    if (!isAuthenticated || !token) {
      console.log('🔌 User not authenticated, skipping socket connection');
      setConnectionError('Please log in to use real-time chat');
      return;
    }

    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    
    console.log('🔌 Initializing Socket.IO connection to:', API_URL);

    // Create socket connection
    const newSocket = io(API_URL, {
      auth: {
        token: token
      },
      transports: ['polling', 'websocket'], // Try polling first, then upgrade to websocket
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: maxReconnectAttempts,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      upgrade: true, // Allow transport upgrade
      rememberUpgrade: false, // Don't remember the upgrade for next time
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('✅ Socket connected:', newSocket.id);
      console.log('🚀 Transport used:', newSocket.io.engine.transport.name);
      console.log('🔗 Socket URL:', newSocket.io.uri);
      setIsConnected(true);
      setConnectionError(null);
      reconnectAttempts.current = 0;
    });

    newSocket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      setIsConnected(false);
      setCurrentChatRoom(null);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        newSocket.connect();
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('🔌 Socket connection error:', error);
      console.error('🔌 Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
      
      setConnectionError(error.message || 'Connection failed');
      setIsConnected(false);
      
      reconnectAttempts.current += 1;
      if (reconnectAttempts.current >= maxReconnectAttempts) {
        setConnectionError('Failed to connect after multiple attempts');
      }
    });

    // Chat event handlers
    newSocket.on('chat_joined', (data) => {
      console.log('🏠 Joined chat room:', data);
      setCurrentChatRoom(data.room);
    });

    newSocket.on('chat_error', (data) => {
      console.error('💬 Chat error:', data);
    });

    newSocket.on('typing_start', (data) => {
      console.log('⌨️ User started typing:', data);
    });

    newSocket.on('typing_stop', (data) => {
      console.log('⌨️ User stopped typing:', data);
    });

    // Authentication error handler
    newSocket.on('error', (error) => {
      console.error('🔐 Socket authentication error:', error);
      if (error.message && error.message.includes('token')) {
        setConnectionError('Authentication failed - please log in again');
        // Clear invalid token
        localStorage.removeItem('token');
      } else {
        setConnectionError('Connection failed');
      }
      setIsConnected(false);
    });

    // Transport upgrade events
    newSocket.io.engine.on('upgrade', () => {
      console.log('🔄 Transport upgraded to:', newSocket.io.engine.transport.name);
    });

    newSocket.io.engine.on('upgradeError', (error) => {
      console.error('❌ Transport upgrade failed:', error);
    });

    setSocket(newSocket);

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up socket connection on unmount/change');
      newSocket.disconnect();
    };
  }, [isLoading, isAuthenticated, token, socket]); // Direct dependencies instead of callback

  const joinChat = useCallback((expertId: string, sessionId?: string) => {
    if (!socket || !isConnected) {
      console.warn('⚠️ Cannot join chat: socket not connected');
      return;
    }

    console.log('🏠 Joining chat:', { expertId, sessionId });
    socket.emit('join_chat', { expertId, sessionId });
  }, [socket, isConnected]);

  const leaveChat = useCallback(() => {
    if (!socket || !isConnected) {
      console.warn('⚠️ Cannot leave chat: socket not connected');
      return;
    }

    console.log('🚪 Leaving chat');
    socket.emit('leave_chat');
    setCurrentChatRoom(null);
  }, [socket, isConnected]);

  const sendChatMessage = useCallback((message: string, expertId: string, sessionId?: string) => {
    if (!socket || !isConnected) {
      console.warn('⚠️ Cannot send message: socket not connected');
      return;
    }

    console.log('📤 Sending chat message:', { message: message.substring(0, 50) + '...', expertId, sessionId });
    socket.emit('start_chat_stream', { message, expertId, sessionId });
  }, [socket, isConnected]);

  const reconnect = useCallback(() => {
    console.log('🔄 Manual reconnection requested');
    if (socket) {
      socket.disconnect();
    }
    setSocket(null);
    setIsConnected(false);
    setConnectionError(null);
    setCurrentChatRoom(null);
    reconnectAttempts.current = 0;
    
    // Force re-initialization by temporarily clearing and restoring token
    // This will trigger the useEffect to run again
    const currentToken = token;
    if (currentToken) {
      // Small delay to ensure cleanup happens first
      setTimeout(() => {
        // The useEffect will automatically reinitialize when dependencies change
        console.log('🔄 Reconnection will be handled by useEffect');
      }, 100);
    }
  }, [socket, token]);

  const value: SocketContextType = {
    socket,
    isConnected,
    connectionError,
    joinChat,
    leaveChat,
    sendChatMessage,
    currentChatRoom,
    reconnect,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketContext;
